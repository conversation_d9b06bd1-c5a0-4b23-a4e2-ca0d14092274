#!/usr/bin/env python3
"""
Simple example demonstrating async self-reflection usage.
"""

import asyncio
from self_reflect import ask_self_reflection_async


async def main():
    """Example of using the async self-reflection function."""
    
    # Single question example
    question = "What is the capital of France?"
    answer = "Paris"
    
    print(f"Question: {question}")
    print(f"Answer: {answer}")
    print("Analyzing with async self-reflection...")
    
    # Use async version for better performance
    score = await ask_self_reflection_async(
        question=question,
        answer=answer,
        num_prompts=3,
        model="gpt-3.5-turbo"
    )
    
    print(f"Self-reflection confidence score: {score}")
    
    # Multiple questions example (concurrent processing)
    print("\n" + "="*50)
    print("Processing multiple questions concurrently...")
    
    questions_and_answers = [
        ("What is 2 + 2?", "4"),
        ("Who wrote Romeo and Juliet?", "William Shakespeare"),
        ("What is the largest planet?", "Jupiter"),
    ]
    
    # Create tasks for concurrent execution
    tasks = []
    for q, a in questions_and_answers:
        task = ask_self_reflection_async(q, a, num_prompts=3)
        tasks.append((q, a, task))
    
    # Execute all concurrently
    print("Running all analyses concurrently...")
    for question, answer, task in tasks:
        score = await task
        print(f"Q: {question}")
        print(f"A: {answer}")
        print(f"Score: {score}")
        print("-" * 30)


if __name__ == "__main__":
    # Make sure to set OPENAI_API_KEY environment variable
    asyncio.run(main())

#!/usr/bin/env python3
"""
Performance comparison between sync and async self-reflection.

This script demonstrates the performance improvements achieved by using
the async version of ask_self_reflection.
"""

import asyncio
import time
from self_reflect import ask_self_reflection, ask_self_reflection_async


async def demo_async_performance():
    """Demonstrate the performance difference between sync and async versions."""
    
    # Test data
    questions_and_answers = [
        ("What is the capital of France?", "Paris"),
        ("What is 2 + 2?", "4"),
        ("Who wrote <PERSON> and Juliet?", "William Shakespeare"),
        ("What is the largest planet in our solar system?", "Jupiter"),
        ("What year did World War II end?", "1945"),
    ]
    
    print("🚀 Performance Comparison: Sync vs Async Self-Reflection")
    print("=" * 60)
    
    # Test synchronous version
    print("\n📊 Testing Synchronous Version (Sequential Processing)...")
    sync_start = time.time()
    
    sync_results = []
    for question, answer in questions_and_answers:
        score = ask_self_reflection(question, answer, num_prompts=3)
        sync_results.append((question, score))
        print(f"  ✓ Processed: {question[:30]}... → Score: {score}")
    
    sync_duration = time.time() - sync_start
    print(f"\n⏱️  Sync Total Time: {sync_duration:.2f} seconds")
    
    # Test asynchronous version
    print("\n⚡ Testing Asynchronous Version (Concurrent Processing)...")
    async_start = time.time()
    
    # Create tasks for concurrent execution
    async_tasks = []
    for question, answer in questions_and_answers:
        task = ask_self_reflection_async(question, answer, num_prompts=3)
        async_tasks.append((question, task))
    
    # Execute all tasks concurrently
    async_results = []
    for question, task in async_tasks:
        score = await task
        async_results.append((question, score))
        print(f"  ✓ Processed: {question[:30]}... → Score: {score}")
    
    async_duration = time.time() - async_start
    print(f"\n⏱️  Async Total Time: {async_duration:.2f} seconds")
    
    # Performance summary
    print("\n📈 Performance Summary:")
    print("=" * 60)
    print(f"Synchronous Time:  {sync_duration:.2f}s")
    print(f"Asynchronous Time: {async_duration:.2f}s")
    
    if async_duration > 0:
        speedup = sync_duration / async_duration
        print(f"Speedup Factor:    {speedup:.2f}x")
        print(f"Time Saved:        {sync_duration - async_duration:.2f}s ({((sync_duration - async_duration) / sync_duration * 100):.1f}%)")
    
    print("\n💡 Key Benefits of Async Version:")
    print("  • Prompts within each question run concurrently")
    print("  • Multiple questions can be processed simultaneously")
    print("  • Better resource utilization during API wait times")
    print("  • Scales better with higher num_prompts values")


async def demo_single_question_async():
    """Demonstrate async performance for a single question with multiple prompts."""
    
    print("\n🔍 Single Question Analysis: Sync vs Async")
    print("=" * 60)
    
    question = "What is the meaning of life according to Douglas Adams?"
    answer = "42"
    
    # Sync version
    print(f"\n📝 Question: {question}")
    print(f"📝 Answer: {answer}")
    
    print("\n⏳ Sync version (prompts run sequentially)...")
    sync_start = time.time()
    sync_score = ask_self_reflection(question, answer, num_prompts=5)
    sync_time = time.time() - sync_start
    print(f"   Result: {sync_score} (took {sync_time:.2f}s)")
    
    # Async version
    print("\n⚡ Async version (prompts run concurrently)...")
    async_start = time.time()
    async_score = ask_self_reflection_async(question, answer, num_prompts=5)
    async_time = time.time() - async_start
    print(f"   Result: {async_score} (took {async_time:.2f}s)")
    
    if async_time > 0:
        speedup = sync_time / async_time
        print(f"\n🚀 Single question speedup: {speedup:.2f}x")


if __name__ == "__main__":
    print("Make sure to set your OPENAI_API_KEY environment variable!")
    print("Example: export OPENAI_API_KEY=sk-...")
    
    # Run the performance demos
    asyncio.run(demo_async_performance())
    asyncio.run(demo_single_question_async())

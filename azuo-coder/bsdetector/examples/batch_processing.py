#!/usr/bin/env python3
"""
Batch processing utility for efficient self-reflection analysis.

This demonstrates how to process many question-answer pairs efficiently
using the async version with proper concurrency control.
"""

import asyncio
import time
from typing import List, Tuple, Optional
from self_reflect import ask_self_reflection_async


async def process_batch_with_concurrency_limit(
    questions_and_answers: List[Tuple[str, str]],
    max_concurrent: int = 5,
    num_prompts: int = 3,
    model: str = "gpt-3.5-turbo"
) -> List[Tuple[str, str, Optional[float]]]:
    """
    Process a batch of questions with concurrency control.
    
    Args:
        questions_and_answers: List of (question, answer) tuples
        max_concurrent: Maximum number of concurrent API calls
        num_prompts: Number of reflection prompts per question
        model: OpenAI model to use
        
    Returns:
        List of (question, answer, score) tuples
    """
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_single(question: str, answer: str) -> <PERSON>ple[str, str, Optional[float]]:
        async with semaphore:
            score = await ask_self_reflection_async(
                question=question,
                answer=answer,
                num_prompts=num_prompts,
                model=model
            )
            return question, answer, score
    
    # Create tasks for all questions
    tasks = [
        process_single(question, answer) 
        for question, answer in questions_and_answers
    ]
    
    # Execute with progress tracking
    results = []
    completed = 0
    total = len(tasks)
    
    print(f"Processing {total} questions with max {max_concurrent} concurrent requests...")
    
    for coro in asyncio.as_completed(tasks):
        result = await coro
        results.append(result)
        completed += 1
        
        question, answer, score = result
        print(f"[{completed}/{total}] {question[:50]}... → {score}")
    
    return results


async def demo_batch_processing():
    """Demonstrate efficient batch processing."""
    
    # Sample dataset
    qa_pairs = [
        ("What is the capital of France?", "Paris"),
        ("What is 2 + 2?", "4"),
        ("Who wrote Romeo and Juliet?", "William Shakespeare"),
        ("What is the largest planet in our solar system?", "Jupiter"),
        ("What year did World War II end?", "1945"),
        ("What is the speed of light?", "299,792,458 meters per second"),
        ("Who painted the Mona Lisa?", "Leonardo da Vinci"),
        ("What is the chemical symbol for gold?", "Au"),
        ("How many continents are there?", "7"),
        ("What is the smallest prime number?", "2"),
        ("Who invented the telephone?", "Alexander Graham Bell"),
        ("What is the capital of Japan?", "Tokyo"),
        ("What is the square root of 64?", "8"),
        ("Who wrote '1984'?", "George Orwell"),
        ("What is the boiling point of water?", "100°C or 212°F"),
    ]
    
    print("🚀 Batch Processing Demo")
    print("=" * 60)
    
    start_time = time.time()
    
    # Process with concurrency control
    results = await process_batch_with_concurrency_limit(
        qa_pairs,
        max_concurrent=3,  # Adjust based on your API rate limits
        num_prompts=3
    )
    
    total_time = time.time() - start_time
    
    # Analyze results
    print("\n📊 Results Summary:")
    print("=" * 60)
    
    successful = [r for r in results if r[2] is not None]
    failed = [r for r in results if r[2] is None]
    
    print(f"Total questions processed: {len(results)}")
    print(f"Successful analyses: {len(successful)}")
    print(f"Failed analyses: {len(failed)}")
    print(f"Success rate: {len(successful)/len(results)*100:.1f}%")
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Average time per question: {total_time/len(results):.2f} seconds")
    
    if successful:
        scores = [r[2] for r in successful]
        avg_score = sum(scores) / len(scores)
        print(f"Average confidence score: {avg_score:.3f}")
        print(f"Score range: {min(scores):.3f} - {max(scores):.3f}")
    
    # Show some examples
    print("\n📝 Sample Results:")
    print("-" * 60)
    for i, (question, answer, score) in enumerate(results[:5]):
        status = "✓" if score is not None else "✗"
        print(f"{status} Q: {question}")
        print(f"   A: {answer}")
        print(f"   Score: {score}")
        print()


async def compare_concurrency_levels():
    """Compare performance at different concurrency levels."""
    
    # Smaller dataset for comparison
    qa_pairs = [
        ("What is the capital of France?", "Paris"),
        ("What is 2 + 2?", "4"),
        ("Who wrote Romeo and Juliet?", "William Shakespeare"),
        ("What is the largest planet?", "Jupiter"),
        ("What year did WWII end?", "1945"),
    ]
    
    print("\n🔬 Concurrency Level Comparison")
    print("=" * 60)
    
    concurrency_levels = [1, 2, 3, 5]
    
    for max_concurrent in concurrency_levels:
        print(f"\nTesting with max_concurrent = {max_concurrent}")
        
        start_time = time.time()
        results = await process_batch_with_concurrency_limit(
            qa_pairs,
            max_concurrent=max_concurrent,
            num_prompts=2  # Fewer prompts for faster testing
        )
        duration = time.time() - start_time
        
        successful = len([r for r in results if r[2] is not None])
        print(f"  Time: {duration:.2f}s, Success: {successful}/{len(results)}")


if __name__ == "__main__":
    print("Make sure to set your OPENAI_API_KEY environment variable!")
    print("Example: export OPENAI_API_KEY=sk-...")
    print("\nNote: Adjust max_concurrent based on your OpenAI API rate limits.")
    print("Higher concurrency = faster processing but may hit rate limits.")
    
    asyncio.run(demo_batch_processing())
    asyncio.run(compare_concurrency_levels())

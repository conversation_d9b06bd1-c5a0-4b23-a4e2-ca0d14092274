#!/usr/bin/env python3
"""
Quick test to verify the async implementation works correctly.
"""

import asyncio
import os
from self_reflect import ask_self_reflection, ask_self_reflection_async


async def test_async_implementation():
    """Test that the async version works and produces reasonable results."""
    
    # Check if API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable not set!")
        print("Please set it with: export OPENAI_API_KEY=sk-...")
        return
    
    print("🧪 Testing Async Self-Reflection Implementation")
    print("=" * 50)
    
    # Simple test case
    question = "What is 2 + 2?"
    answer = "4"
    
    print(f"Question: {question}")
    print(f"Answer: {answer}")
    print()
    
    try:
        # Test async version
        print("Testing async version...")
        async_score = await ask_self_reflection_async(
            question=question,
            answer=answer,
            num_prompts=2,  # Use fewer prompts for faster testing
            model="gpt-3.5-turbo"
        )
        print(f"✅ Async score: {async_score}")
        
        # Test sync version for comparison
        print("Testing sync version...")
        sync_score = ask_self_reflection(
            question=question,
            answer=answer,
            num_prompts=2,
            model="gpt-3.5-turbo"
        )
        print(f"✅ Sync score: {sync_score}")
        
        # Verify both versions work
        if async_score is not None and sync_score is not None:
            print("\n🎉 Both implementations working correctly!")
            print(f"Scores are similar: async={async_score:.3f}, sync={sync_score:.3f}")
        else:
            print("\n⚠️  One or both implementations returned None")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        print("This might be due to API key issues or network problems.")


if __name__ == "__main__":
    asyncio.run(test_async_implementation())

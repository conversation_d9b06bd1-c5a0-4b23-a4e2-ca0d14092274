# BSDETECTOR

A lightweight Python library to estimate uncertainty of LLM answers via **self-reflection**. It implements the *self-reflection certainty* method from the paper:

> **Quantifying Uncertainty in Answers from any Language Model and Enhancing their Trustworthiness**  
> <PERSON> & Mueller, ACL 2024  
> [arXiv:2308.16175](https://arxiv.org/abs/2308.16175)

## 🔍 What It Does

Given a question and an answer from an LLM, this library asks the LLM to evaluate its own answer using a series of multiple-choice follow-up prompts. Each response is scored as:

- **A = 1.0** (Correct)
- **B = 0.0** (Incorrect)
- **C = 0.5** (Uncertain)

The final uncertainty score is the **average** over all follow-up responses.

## 🚀 Installation

```bash
poetry install
poetry shell
```

Make sure to set your OpenAI API key:

```bash
export OPENAI_API_KEY=sk-...
```

## 🧠 Example Usage

```python
from self_reflect import ask_self_reflection

question = "What is 1 + 1?"
answer = "2"

score = ask_self_reflection(question, answer, num_prompts=3)
print(f"Self-reflection certainty score: {score}")
```

## ⚙️ API

```python
ask_self_reflection(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None
```

### Parameters

- `question`: the original question posed to the LLM
- `answer`: the original answer you want to evaluate
- `model`: OpenAI model name
- `temperature`: LLM generation temperature (should stay 0.0 for consistency)
- `num_prompts`: number of reflection prompts (2, 3, 4, or 5)
- `max_retries`: how many times to retry the API on failure

Returns a float in `[0.0, 1.0]` or `None` if not all prompts succeed.

## 🛠️ Prompt Templates

The library currently includes 5 prewritten prompt templates that progressively vary in phrasing. Only the number of prompts specified by `num_prompts` will be used.

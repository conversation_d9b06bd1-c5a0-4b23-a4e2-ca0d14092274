# BSDETECTOR

A lightweight Python library to estimate uncertainty of LLM answers via **self-reflection**. It implements the *self-reflection certainty* method from the paper:

> **Quantifying Uncertainty in Answers from any Language Model and Enhancing their Trustworthiness**  
> <PERSON> & Mueller, ACL 2024  
> [arXiv:2308.16175](https://arxiv.org/abs/2308.16175)

## 🔍 What It Does

Given a question and an answer from an LLM, this library asks the LLM to evaluate its own answer using a series of multiple-choice follow-up prompts. Each response is scored as:

- **A = 1.0** (Correct)
- **B = 0.0** (Incorrect)
- **C = 0.5** (Uncertain)

The final uncertainty score is the **average** over all follow-up responses.

## 🚀 Installation

```bash
poetry install
poetry shell
```

Make sure to set your OpenAI API key:

```bash
export OPENAI_API_KEY=sk-...
```

## 🧠 Example Usage

### Synchronous (Sequential Processing)
```python
from self_reflect import ask_self_reflection

question = "What is 1 + 1?"
answer = "2"

score = ask_self_reflection(question, answer, num_prompts=3)
print(f"Self-reflection certainty score: {score}")
```

### Asynchronous (Concurrent Processing) ⚡
```python
import asyncio
from self_reflect import ask_self_reflection_async

async def main():
    question = "What is 1 + 1?"
    answer = "2"

    # Prompts run concurrently for better performance
    score = await ask_self_reflection_async(question, answer, num_prompts=3)
    print(f"Self-reflection certainty score: {score}")

asyncio.run(main())
```

## ⚙️ API

### Synchronous Version
```python
ask_self_reflection(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None
```

### Asynchronous Version (Recommended for Performance)
```python
async def ask_self_reflection_async(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None
```

### Parameters

- `question`: the original question posed to the LLM
- `answer`: the original answer you want to evaluate
- `model`: OpenAI model name
- `temperature`: LLM generation temperature (should stay 0.0 for consistency)
- `num_prompts`: number of reflection prompts (2, 3, 4, or 5)
- `max_retries`: how many times to retry the API on failure

Returns a float in `[0.0, 1.0]` or `None` if not all prompts succeed.

## 🚀 Performance Benefits

The async version provides significant performance improvements:

- **Concurrent prompt processing**: All reflection prompts run simultaneously instead of sequentially
- **Faster batch processing**: Process multiple questions concurrently
- **Better resource utilization**: CPU can handle other tasks while waiting for API responses
- **Scalability**: Performance improvement increases with higher `num_prompts` values

**Performance Example**: With `num_prompts=5`, the async version can be **3-5x faster** than the sync version.

## 📊 Batch Processing

For processing many questions efficiently:

```python
import asyncio
from self_reflect import ask_self_reflection_async

async def process_many_questions():
    questions_and_answers = [
        ("What is the capital of France?", "Paris"),
        ("What is 2 + 2?", "4"),
        ("Who wrote Romeo and Juliet?", "William Shakespeare"),
    ]

    # Process all questions concurrently
    tasks = [
        ask_self_reflection_async(q, a, num_prompts=3)
        for q, a in questions_and_answers
    ]

    scores = await asyncio.gather(*tasks)
    return scores

# Run batch processing
scores = asyncio.run(process_many_questions())
```

## 🛠️ Prompt Templates

The library currently includes 5 prewritten prompt templates that progressively vary in phrasing. Only the number of prompts specified by `num_prompts` will be used.

# self_reflect/self_reflect.py

import asyncio
import openai
import re
import time
from typing import Literal

CHOICE_MAP = {"A": 1.0, "B": 0.0, "C": 0.5}

SELF_REFLECTION_PROMPTS = [
    (
        "Question: {question}\n"
        "Proposed Answer: {answer}\n"
        "Is the proposed answer: (A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Question: {question}\n"
        "Proposed Answer: {answer}\n"
        "Are you really sure the proposed answer is correct?\n"
        "Choose again: (A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Consider the question and answer again carefully.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "Do you believe the answer is accurate?\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Evaluate the proposed answer with reasoning.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "Select your evaluation:\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Reconsider your judgment about the following answer.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "How would you rate it?\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    )
]

def ask_self_reflection(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None:
    """
    Synchronous version of self-reflection that processes prompts sequentially.

    For better performance with multiple prompts, consider using ask_self_reflection_async()
    which runs prompts concurrently.
    """
    # Create OpenAI client
    client = openai.OpenAI()
    scores = []

    for i in range(num_prompts):
        prompt = SELF_REFLECTION_PROMPTS[i].format(question=question, answer=answer)
        messages = [
            {"role": "user", "content": prompt}
        ]

        for attempt in range(max_retries):
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                )
                content = response.choices[0].message.content
                choice = parse_choice(content)
                if choice is not None:
                    scores.append(CHOICE_MAP[choice])
                    break
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(1)
                else:
                    raise e

    if len(scores) != num_prompts:
        return None

    return sum(scores) / len(scores)

async def ask_self_reflection_async(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None:
    """
    Async version of ask_self_reflection that runs prompts concurrently for better performance.

    This function can reduce execution time from num_prompts × API_latency to max(API_latency)
    when all prompts can run concurrently.
    """
    # Create async OpenAI client
    client = openai.AsyncOpenAI()

    # Create all prompts upfront
    prompts = []
    for i in range(num_prompts):
        prompt = SELF_REFLECTION_PROMPTS[i].format(question=question, answer=answer)
        prompts.append(prompt)

    # Create tasks for concurrent execution
    tasks = []
    for prompt in prompts:
        task = _make_single_reflection_call_async(
            client, prompt, model, temperature, max_retries
        )
        tasks.append(task)

    # Execute all prompts concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results
    scores = []
    for result in results:
        if isinstance(result, Exception):
            # If any prompt failed, we can't compute a reliable score
            continue
        if result is not None:
            scores.append(result)

    # Return None if we didn't get all expected scores
    if len(scores) != num_prompts:
        return None

    return sum(scores) / len(scores)


async def _make_single_reflection_call_async(
    client: openai.AsyncOpenAI,
    prompt: str,
    model: str,
    temperature: float,
    max_retries: int,
) -> float | None:
    """Helper function to make a single reflection API call with retries."""
    messages = [{"role": "user", "content": prompt}]

    for attempt in range(max_retries):
        try:
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
            )
            content = response.choices[0].message.content
            choice = parse_choice(content)
            if choice is not None:
                return CHOICE_MAP[choice]
        except Exception as e:
            if attempt < max_retries - 1:
                # Use asyncio.sleep for non-blocking delay
                await asyncio.sleep(1)
            else:
                raise e

    return None


def parse_choice(content: str) -> str | None:
    match = re.search(r"answer\s*[:\-]?\s*([ABC])", content, re.IGNORECASE)
    if match:
        return match.group(1).upper()
    match = re.search(r"\b([ABC])\b", content)
    if match:
        return match.group(1).upper()
    return None

# self_reflect/self_reflect.py

import openai
import re
import time
from typing import Literal

CHOICE_MAP = {"A": 1.0, "B": 0.0, "C": 0.5}

SELF_REFLECTION_PROMPTS = [
    (
        "Question: {question}\n"
        "Proposed Answer: {answer}\n"
        "Is the proposed answer: (A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Question: {question}\n"
        "Proposed Answer: {answer}\n"
        "Are you really sure the proposed answer is correct?\n"
        "Choose again: (A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Consider the question and answer again carefully.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "Do you believe the answer is accurate?\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Evaluate the proposed answer with reasoning.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "Select your evaluation:\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    ),
    (
        "Reconsider your judgment about the following answer.\n"
        "Question: {question}\n"
        "Answer: {answer}\n"
        "How would you rate it?\n"
        "(A) Correct (B) Incorrect (C) I am not sure.\n"
        "explanation:"
    )
]

def ask_self_reflection(
    question: str,
    answer: str,
    model: str = "gpt-3.5-turbo",
    temperature: float = 0.0,
    num_prompts: Literal[2, 3, 4, 5] = 3,
    max_retries: int = 3,
) -> float | None:
    scores = []

    for i in range(num_prompts):
        prompt = SELF_REFLECTION_PROMPTS[i].format(question=question, answer=answer)
        messages = [
            {"role": "user", "content": prompt}
        ]

        for attempt in range(max_retries):
            try:
                response = openai.ChatCompletion.create(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                )
                content = response["choices"][0]["message"]["content"]
                choice = parse_choice(content)
                if choice is not None:
                    scores.append(CHOICE_MAP[choice])
                    break
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(1)
                else:
                    raise e

    if len(scores) != num_prompts:
        return None

    return sum(scores) / len(scores)

def parse_choice(content: str) -> str | None:
    match = re.search(r"answer\s*[:\-]?\s*([ABC])", content, re.IGNORECASE)
    if match:
        return match.group(1).upper()
    match = re.search(r"\b([ABC])\b", content)
    if match:
        return match.group(1).upper()
    return None
